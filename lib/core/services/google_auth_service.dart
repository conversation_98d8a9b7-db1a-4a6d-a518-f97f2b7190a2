import 'package:google_sign_in/google_sign_in.dart';
import '../widgets/widgets.dart';

class GoogleAuthService {
  static final GoogleSignIn _googleSignIn = GoogleSignIn(
    scopes: [
      'email',
      'profile',
    ],
  );

  static Future<String?> signInWithGoogle() async {
    try {
      Widgets.showLoader("Signing in with Google...");

      // Check if user is already signed in
      final currentUser = await _googleSignIn.signInSilently();
      if (currentUser != null) {
        await _googleSignIn.signOut();
      }

      // Trigger the authentication flow
      final GoogleSignInAccount? googleUser = await _googleSignIn.signIn();

      if (googleUser == null) {
        Widgets.hideLoader();
        Widgets.showSnackBar('Cancelled', 'Google Sign-In was cancelled');
        return null;
      }

      // Obtain the auth details from the request
      final GoogleSignInAuthentication googleAuth = await googleUser.authentication;

      // Return the access token
      return googleAuth.accessToken;

    } catch (error) {
      print('Google Sign-In Error: $error');

      // Handle specific error codes
      String errorMessage = 'Failed to sign in with Google';
      if (error.toString().contains('sign_in_failed')) {
        if (error.toString().contains('10:')) {
          errorMessage = 'Google Sign-In configuration error. Please contact support.';
        } else if (error.toString().contains('7:')) {
          errorMessage = 'Network error. Please check your internet connection.';
        }
      }

      Widgets.showSnackBar('Error', errorMessage);
      return null;
    } finally {
      Widgets.hideLoader();
    }
  }

  static Future<void> signOut() async {
    await _googleSignIn.signOut();
  }
}