import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get_rx/src/rx_typedefs/rx_typedefs.dart';
import 'package:music_app/core/constants/assets_constants.dart';
import 'package:music_app/core/constants/color_constants.dart';
import 'package:music_app/core/widgets/text_widgets.dart';
import 'package:music_app/core/widgets/widgets.dart';
import 'package:music_app/view/musician/bottom_navigation/modules/home/<USER>/members_model.dart';

class MemberCard extends StatelessWidget {
  MemberCard({
    super.key,
    this.bgColor,
    this.height,this.onFavTap,this.notFav,
    required this.member,
  });
  Color? bgColor;
  var height;Callback? onFavTap;

  Members? member;bool? notFav;
  @override
  Widget build(BuildContext context) {
    return Container(
      height: height,
      decoration: BoxDecoration(
        color: bgColor,
        border: Border.all(color: ColorConstants.grayBorderColor, width: 1),
        borderRadius: BorderRadius.circular(10),
      ),
      child: Padding(
        padding: const EdgeInsets.all(8.0),
        child: Row(
          crossAxisAlignment: CrossAxisAlignment.center,
          children: [
            Stack(
              clipBehavior: Clip.none,
              alignment: Alignment.bottomRight,
              children: [
                SizedBox(
                  height: 80.h,
                  width: 80.w,
                  child: ClipRRect(
                    borderRadius: BorderRadius.circular(8),
                    child: Widgets.networkImage(
                      member?.imageUrl ?? "",
                    ),
                  ),
                ),
                Positioned(
                  right: -5,
                  bottom: -4,
                  child: member?.availability == 1
                      ? Container(
                          width: 12,
                          height: 12,
                          decoration: BoxDecoration(
                            shape: BoxShape.circle,
                            color: ColorConstants.greenColor,
                            border: Border.all(
                                color: ColorConstants.whiteColor, width: 2),
                          ),
                        )
                      : SizedBox.shrink(),
                )
              ],
            ),
            Widgets.widthSpaceW2,
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      Expanded(
                        child: Texts.textBold(member?.name ?? "",
                            textAlign: TextAlign.start, size: 14),
                      ),
                      notFav==true?SizedBox():GestureDetector(
                        onTap:onFavTap,
                        child:Icon(
                          member?.isFavorite == true
                              ? Icons.favorite
                              : Icons.favorite_border,
                          color: ColorConstants.redColor,
                          size: 20,
                        ),
                      ),
                    ],
                  ),
                  Texts.textNormal(
                    member?.tags?.join(", ") ?? "",
                    size: 10,
                    textAlign: TextAlign.start,
                  ),
                  SizedBox(height: 2),
                  Row(
                    crossAxisAlignment: CrossAxisAlignment.center,
                    children: [
                      SizedBox(
                          height: 11,
                          child: Image.asset(
                            Assets.locationIcon,
                            fit: BoxFit.contain,
                          )),
                      SizedBox(width: 4),
                      Expanded(
                        child: Texts.textNormal(
                          member?.location ?? "",
                          size: 10,
                          textAlign: TextAlign.start,
                        ),
                      ),
                    ],
                  ),
                  Widgets.heightSpaceH05,
                  Row(crossAxisAlignment: CrossAxisAlignment.center,
                    children: [
                      Icon(Icons.star, color: Colors.orange, size: 14),
                      Widgets.heightSpaceH05,
                      RichText(
                          text: TextSpan(children: [
                        TextSpan(
                            text: member?.averageRating ?? "0",
                            style: TextStyle(
                                color: ColorConstants.blackColor,fontFamily: "PoppinsRegular",

                                fontSize: 10)),
                        TextSpan(
                            text: ' (${member?.ratingsCount ?? "0"})',
                            style: TextStyle(
                                color: ColorConstants.greyTextColor,fontFamily: "PoppinsRegular",
                                fontWeight: FontWeight.normal,
                                fontSize: 10)),
                      ])),
                      Expanded(child: Widgets.heightSpaceH05),
                      Column(
                        crossAxisAlignment: CrossAxisAlignment.end,
                        children: [
                          Texts.textBold(
                            "\$${member?.ratePerHour??"0"}",
                           size: 12,
                          ),
                          Texts.textNormal(
                            "Per Hour",size: 8,color: ColorConstants.blackColor,
                          ),
                        ],
                      ),
                    ],
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }
}
class SearchMemberCard extends StatelessWidget {
  SearchMemberCard({
    super.key,
    this.bgColor,    this.borderColor,
    this.height,this.onFavTap,
    required this.member,required this.isCompare,this.onCompareTap,
  });
  Color? bgColor; Color? borderColor;
  var height;Callback? onFavTap;  Callback? onCompareTap;bool? isCompare;
  Members? member;
  @override
  Widget build(BuildContext context) {
    return Container(
      height: height,
      decoration: BoxDecoration(
        color: bgColor,
        border: Border.all(color: borderColor??ColorConstants.grayBorderColor, width: 1),
        borderRadius: BorderRadius.circular(10),
      ),
      child: Padding(
        padding: const EdgeInsets.all(8.0),
        child: Row(
          crossAxisAlignment: CrossAxisAlignment.center,
          children: [
            Stack(
              clipBehavior: Clip.none,
              alignment: Alignment.bottomRight,
              children: [
                SizedBox(
                  height: 80.h,
                  width: 80.w,
                  child: ClipRRect(
                    borderRadius: BorderRadius.circular(8),
                    child: Widgets.networkImage(
                      member?.imageUrl ?? "",
                    ),
                  ),
                ),
                Positioned(
                  right: -5,
                  bottom: -4,
                  child: member?.availability == 1
                      ? Container(
                    width: 12,
                    height: 12,
                    decoration: BoxDecoration(
                      shape: BoxShape.circle,
                      color: ColorConstants.greenColor,
                      border: Border.all(
                          color: ColorConstants.whiteColor, width: 2),
                    ),
                  )
                      : SizedBox.shrink(),
                )
              ],
            ),
            Widgets.widthSpaceW2,
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      Expanded(
                        child: Texts.textBold(member?.name ?? "",
                            textAlign: TextAlign.start, size: 14),
                      ),Texts.textNormal("Compare",size: 10),
                      GestureDetector(
                        onTap:onCompareTap,
                        child:Icon(
                          isCompare == false
                              ? Icons.check_box_outline_blank
                              : Icons.check_box,
                          color: ColorConstants.primaryColor,
                          size: 20,
                        ),
                      ),
                      // GestureDetector(
                      //   onTap:onFavTap,
                      //   child:Icon(
                      //     member?.isFavorite == true
                      //         ? Icons.favorite
                      //         : Icons.favorite_border,
                      //     color: ColorConstants.redColor,
                      //     size: 20,
                      //   ),
                      // ),
                    ],
                  ),
                  Texts.textNormal(
                    member?.tags?.join(", ") ?? "",
                    size: 10,
                    textAlign: TextAlign.start,
                  ),
                  SizedBox(height: 2),
                  Row(
                    crossAxisAlignment: CrossAxisAlignment.center,
                    children: [
                      SizedBox(
                          height: 11,
                          child: Image.asset(
                            Assets.locationIcon,
                            fit: BoxFit.contain,
                          )),
                      SizedBox(width: 4),
                      Expanded(
                        child: Texts.textNormal(
                          member?.location ?? "",
                          size: 10,
                          textAlign: TextAlign.start,
                        ),
                      ),
                    ],
                  ),
                  Widgets.heightSpaceH05,
                  Row(crossAxisAlignment: CrossAxisAlignment.center,
                    children: [
                      Icon(Icons.star, color: Colors.orange, size: 14),
                      Widgets.heightSpaceH05,
                      RichText(
                          text: TextSpan(children: [
                            TextSpan(
                                text: member?.averageRating ?? "0",
                                style: TextStyle(
                                    color: ColorConstants.blackColor,fontFamily: "PoppinsRegular",

                                    fontSize: 10)),
                            TextSpan(
                                text: ' (${member?.ratingsCount ?? "0"})',
                                style: TextStyle(
                                    color: ColorConstants.greyTextColor,fontFamily: "PoppinsRegular",
                                    fontWeight: FontWeight.normal,
                                    fontSize: 10)),
                          ])),
                      Expanded(child: Widgets.heightSpaceH05),
                      Column(
                        crossAxisAlignment: CrossAxisAlignment.end,
                        children: [
                          Texts.textBold(
                            "\$${member?.ratePerHour??"0"}",
                            size: 12,
                          ),
                          Texts.textNormal(
                            "Per Hour",size: 8,color: ColorConstants.blackColor,
                          ),
                        ],
                      ),
                    ],
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }
}
