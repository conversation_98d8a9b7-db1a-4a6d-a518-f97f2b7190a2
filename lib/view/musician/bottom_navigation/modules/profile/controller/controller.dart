

import 'dart:convert';

import 'package:country_picker/country_picker.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:http/http.dart' as http;
import 'package:music_app/core/constants/api_endpoints.dart';
import 'package:music_app/core/services/http_service.dart';
import 'package:music_app/core/widgets/widgets.dart';
import 'package:music_app/controller/user_controller.dart';
import 'package:music_app/model/gallery._model.dart';

import '../../../../../../core/constants/assets_constants.dart';
import '../../../../../../core/constants/color_constants.dart';
import '../../../../../../model/user_model.dart';
import '../../../../create_musician_profile/about_you_step_six.dart';

class MusicianController extends GetxController{
  // Explicitly make these RxString to track changes
  RxString? profileImagePath = RxString('');
  RxString? coverImagePath = RxString('');  RxString? galleryImagePath = RxString('');
  final raterHour = TextEditingController();
  final rateEvent = TextEditingController();
  final tagController = TextEditingController();
  final websiteController = TextEditingController();
  RxList<String> tags = <String>[].obs;  RxList<Gallery> gallery= <Gallery>[].obs;
  RxList<String> selectedLanguages = <String>[].obs;
  RxList<SocialMedia> socialMediaList = <SocialMedia>[].obs;
  RxList<String> selectedServices = <String>[].obs;
  RxList<String> selectedInstruments = <String>[].obs;
  // Change from single selection to multi-selection
  RxList<String> selectedPaymentMethods = <String>[].obs;

  // Toggle payment method selection
  void togglePaymentMethod(String method) {
    if (selectedPaymentMethods.contains(method)) {
      selectedPaymentMethods.remove(method);
    } else {
      selectedPaymentMethods.add(method);
    }
  }

  // Check if a payment method is selected
  bool isMethodSelected(String method) {
    return selectedPaymentMethods.contains(method);
  }
  void toggleInstrument(String instrument) {
    if (selectedInstruments.contains(instrument)) {
      selectedInstruments.remove(instrument);
    } else {
      selectedInstruments.add(instrument);
    }
  }

  bool isInstrumentSelected(String instrument) {
    return selectedInstruments.contains(instrument);
  }
  void toggleService(String service) {
    if (selectedServices.contains(service)) {
      selectedServices.remove(service);
    } else {
      selectedServices.add(service);
    }
  }

  bool isServiceSelected(String service) {
    return selectedServices.contains(service);
  }
  final nameController = TextEditingController();
  final descController = TextEditingController();
  final phoneController = TextEditingController();
  final locationController = TextEditingController();
  RxString selectedCountryCode = "1".obs;
  RxString selectedLatitude = "".obs;
  RxString selectedLongitude = "".obs;
  RxList<RxBool> rolesSelection = List.generate(6, (_) => false.obs).obs;
  RxList<RxBool> playedInstrument = List.generate(6, (_) => false.obs).obs;
  var isAvailable = true.obs;
  var isLiveLocation = true.obs;
  final userController = Get.find<UserController>();

  @override
  void onInit() {
    super.onInit();
  userController.fetchUserDetails();
    isAvailable.value = userController.userModel?.availability == 1;
    isLiveLocation.value = userController.userModel?.liveLocation == 1;
  }
  void addTag(String tag) {
    if (tag.isNotEmpty && !tags.contains(tag)) {
      tags.add(tag);
      tagController.clear();
    }
  }
  RxList<String> selectedRoles = <String>[].obs;

  final RxList<Map<String, dynamic>> roles = [
    {'label': 'Musician', 'icon': Assets.whiteguitarImage},
    {'label': 'DJ', 'icon': Assets.djImage},
    {'label': 'Manager', 'icon': Assets.managerImage},
    {'label': 'Tech Support', 'icon': Assets.techSupportIcon},
  ].obs;

  void toggleRoleEdit(String role) {
    if (selectedRoles.contains(role)) {
      selectedRoles.remove(role);
    } else {
      selectedRoles.add(role);
    }
  }

  bool isRoleSelected(String role) {
    return selectedRoles.contains(role);
  } RxList<String> selectedMusicTypes = <String>[].obs;

  void toggleMusicType(String musicType) {
    if (selectedMusicTypes.contains(musicType)) {
      selectedMusicTypes.remove(musicType);
    } else {
      selectedMusicTypes.add(musicType);
    }
  }

  bool isMusicTypeSelected(String musicType) {
    return selectedMusicTypes.contains(musicType);
  }
  void removeTag(String tag) {
    tags.remove(tag);
  }

  void toggleLanguage(String language) {
    if (selectedLanguages.contains(language)) {
      selectedLanguages.remove(language);
    } else {
      selectedLanguages.add(language);
    }
  }

  void addSocialMedia() {
    socialMediaList.add(SocialMedia(
      type: 'Instagram',
      username: '',
    ));
  }

  void removeSocialMedia(int index) {
    if (index >= 0 && index < socialMediaList.length) {
      socialMediaList.removeAt(index);
    }
  }

  void updateSocialMediaType(int index, String type) {
    if (index >= 0 && index < socialMediaList.length) {
      socialMediaList[index] = socialMediaList[index].copyWith(type: type);
    }
  }

  void updateSocialMediaUsername(int index, String username) {
    if (index >= 0 && index < socialMediaList.length) {
      socialMediaList[index] =
          socialMediaList[index].copyWith(username: username);
    }
  }

  String getSocialMediaUrl(String type, String username) {
    switch (type) {
      case 'Instagram':
        return 'https://instagram.com/$username';
      case 'Facebook':
        return 'https://facebook.com/$username';
      case 'Twitter':
        return 'https://twitter.com/$username';
      case 'TikTok':
        return 'https://tiktok.com/@$username';
      case 'YouTube':
        return 'https://youtube.com/@$username';
      case 'LinkedIn':
        return 'https://linkedin.com/in/$username';
      case 'SoundCloud':
        return 'https://soundcloud.com/$username';
      case 'Spotify':
        return 'https://open.spotify.com/artist/$username';
      default:
        return username;
    }
  }
  pickCountryCodeBottomSheet(BuildContext context) {
    showCountryPicker(
      context: context,
      countryListTheme: CountryListThemeData(searchTextStyle: TextStyle(color: Colors.black87),
          textStyle: const TextStyle(color: Colors.black),
          bottomSheetHeight: .60.sh,
          inputDecoration: InputDecoration(
              prefixIcon: Icon(CupertinoIcons.search),
              border: OutlineInputBorder(
                borderSide: BorderSide(color: ColorConstants.grayBorderColor),
                borderRadius: const BorderRadius.all(Radius.circular(10)),
              ),
              focusedBorder: OutlineInputBorder(
                borderSide: BorderSide(color: ColorConstants.primaryColor),
                borderRadius: const BorderRadius.all(Radius.circular(10)),
              ),
              enabledBorder: OutlineInputBorder(
                borderSide: BorderSide(color: ColorConstants.grayBorderColor),
                borderRadius: const BorderRadius.all(Radius.circular(10)),
              ),
              hintText: "Search Country")),
      showPhoneCode: true, // Set to true if you need phone codes
      onSelect: (Country country) {print(country.phoneCode);
      selectedCountryCode.value = country.phoneCode;

      },
    );
  }

  void toggleRole(int index) {
    rolesSelection[index].value = !rolesSelection[index].value;
  }

  void toggleInstruments(int index) {
    playedInstrument[index].value = !playedInstrument[index].value;
  }

  void toggleAvailability(bool value) async {
    isAvailable.value = value;

    try {


      final response = await ApiService.postData(
        Endpoints.updateAvailability,
        {"availability": value ? 1 : 0}
      );



      if (response.status == true) {
        // Update the user model

          userController.userModel?.availability = value ? 1 : 0;
          userController.update();

      } else {
        // Revert the switch if API call fails
        isAvailable.value = !value;
      }
    } catch (e) {

      isAvailable.value = !value; // Revert on error
    }
  }

  void toggleLiveLocation(bool value) async {
    isLiveLocation.value = value;

    try {


      final response = await ApiService.postData(
        Endpoints.updateLiveLocation,
        {"live_location": value ? 1 : 0}
      );


      if (response.status == true) {

          userController.userModel?.liveLocation = value ? 1 : 0;
          userController.update();

      } else {
        // Revert the switch if API call fails
        isLiveLocation.value = !value;
      }
    } catch (e) {
      Widgets.hideLoader(); isLiveLocation.value = !value; // Revert on error
    }
  }

  changeProfilePic() async {
    Widgets.showLoader("Changing profile picture...");
    var request = http.MultipartRequest('POST',
        Uri.parse('${Endpoints.baseURL}${Endpoints.updateProfileImage}'));
    var pic = await http.MultipartFile.fromPath('image',profileImagePath?.value ?? "");
    request.files.add(pic);
    request.headers['Authorization'] = 'Bearer ${userController.token ?? ""}';

    var response = await request.send();

    Widgets.hideLoader();

    if (response.statusCode == 200) {
      var data = await response.stream.bytesToString();
      var decodedData = jsonDecode(data);

      if (decodedData['status'] == true) {
        UserModel userModel = UserModel.fromJson(decodedData['user']);
        await userController.saveUser(userModel, userController.token ?? "");
        userController.fetchUser();

      } else {
      }
    } else {
      throw Exception('Failed to send data to the server');
    }
  }
  addGalleryPic() async {
    Widgets.showLoader("Loading");
    var request = http.MultipartRequest('POST',
        Uri.parse('${Endpoints.baseURL}${Endpoints.addGallery}'));
    var pic =
    await http.MultipartFile.fromPath('images[]', galleryImagePath?.value ?? "");
    request.files.add(pic);
    request.headers['Authorization'] = 'Bearer ${userController.token ?? ""}';

    var response = await request.send();

    Widgets.hideLoader();
    var data = await response.stream.bytesToString();
    var decodedData = jsonDecode(data);
    if (response.statusCode == 200) {
      var data = await response.stream.bytesToString();
      var decodedData = jsonDecode(data);

      if (decodedData['status'] == true) {

        // Widgets.showSnackBar("Success", "Banner Updated");
      } else {
      }
    } else {
      throw Exception('Failed to send data to the server');
    }
  }
  deleteGalleryPic(int id, int index) async {

    userController
        .userModel?.musicianProfile!.gallery
        ?.removeAt(index);userController.update();
    try {

      final response = await ApiService.getData(
          Endpoints.deleteGalleryImage+"/$id",
      );

      if (response.status == true) {


      } else {

      }
    } catch (e) {

    }
  }

  changeBannerPic() async {
    Widgets.showLoader("Changing cover image....");
    var request = http.MultipartRequest('POST',
        Uri.parse('${Endpoints.baseURL}${Endpoints.updateBanner}'));
    var pic =
    await http.MultipartFile.fromPath('header_image', coverImagePath?.value ?? "");
    request.files.add(pic);
    request.headers['Authorization'] = 'Bearer ${userController.token ?? ""}';

    var response = await request.send();

    Widgets.hideLoader();

    if (response.statusCode == 200) {
      var data = await response.stream.bytesToString();
      var decodedData = jsonDecode(data);

      if (decodedData['status'] == true) {
        UserModel userModel = UserModel.fromJson(decodedData['user']);
        await userController.saveUser(userModel, userController.token ?? "");
        userController.fetchUser();
        // Widgets.showSnackBar("Success", "Banner Updated");
      } else {
      }
    } else {
      throw Exception('Failed to send data to the server');
    }
  }
  updateMusicianProfile() async {
    try {

      Widgets.showLoader("Updating Profile...");


      List tempLinks=[];
      for (var item in socialMediaList)
      {

        tempLinks.add({

          {
            "key":item.type,"value":getSocialMediaUrl(item.type, item.username)
          }
        });
      }

      // Prepare API payload
      var payload = {
        'name': nameController.text,
        'description': descController.text,
        'phone_number':
        '${phoneController.text}',"country_code":"${selectedCountryCode.value}",
        'spoken_languages': selectedLanguages.toList(),
        'offered_services': selectedServices.toList(),
        'roles': selectedRoles.toList(),
        'instruments': selectedInstruments.toList(),
        'music_types': selectedMusicTypes.toList(),
        'payment_methods': selectedPaymentMethods.toList(),
        'rate_per_hour': raterHour.text,
        'rate_per_event': rateEvent.text,
        'social_links':tempLinks.toList(),
        'tags': tags.toList(),
        'location': locationController.text,"website":websiteController.text,
        'latitude': selectedLatitude.value,
        'longitude': selectedLongitude.value,
      };


      final response =
      await ApiService.postData(Endpoints.updateProfile, payload);
      Widgets.hideLoader();
      if (response.status == true) {

        UserModel userModel = UserModel.fromJson(response.data['user']);
        await userController.saveUser(userModel, userController.token ?? "");
        userController.fetchUser();
        Widgets.showSnackBar(
            "Success", response.message ?? "Profile updated");
      } else {
        Widgets.hideLoader();
        // Widgets.showSnackBar(
        //     "Error", response.message ?? "Failed to update profile");
      }
    } catch (e) {
      Widgets.hideLoader();
      // Widgets.showSnackBar("Error", "Something went wrong. Please try again.");
      print("Profile update error: $e");
    } finally {
      Widgets.hideLoader();
    }
  }


}