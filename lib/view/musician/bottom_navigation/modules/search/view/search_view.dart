import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:music_app/core/constants/assets_constants.dart';
import 'package:music_app/core/constants/color_constants.dart';
import 'package:music_app/core/constants/padding_constants.dart';
import 'package:music_app/core/routes/app_routes.dart';
import 'package:music_app/core/utils/extensions.dart';
import 'package:music_app/core/widgets/custom_button.dart';
import 'package:music_app/core/widgets/entry_field.dart';
import 'package:music_app/core/widgets/my_list_tile.dart';
import 'package:music_app/core/widgets/widgets.dart';
import 'package:google_maps_flutter/google_maps_flutter.dart';
import 'package:music_app/view/musician/bottom_navigation/modules/home/<USER>/home_controller.dart';
import 'package:music_app/view/musician/bottom_navigation/modules/search/view/comparison_view.dart';
import 'package:music_app/view/musician/bottom_navigation/modules/search/view/filter_view.dart';
import 'package:music_app/view/musician/subscription/view/subscribe_view.dart';

import '../../../../../../controller/user_controller.dart';
import '../../../../../../core/widgets/shimmer_effect.dart';
import '../../home/<USER>/members_model.dart';
import '../../home/<USER>/member_detail_view.dart';
import '../controller/search_controller.dart';

class SearchMemberView extends StatefulWidget {
  const SearchMemberView();

  @override
  State<SearchMemberView> createState() => _SearchMemberViewState();
}

class _SearchMemberViewState extends State<SearchMemberView> {
  final ScrollController scrollController = ScrollController();
  late SearchMemberController controller;
  final FocusNode searchFocusNode = FocusNode();

  @override
  void initState() {
    super.initState();
    controller = Get.put(SearchMemberController());
    controller.isSearchActive.value = false;
    controller.isMoreLoading.value = false;
    controller.isLoading.value = false;
    controller.clearFilters();

    scrollController.addListener(scrollListener);

    // Hide keyboard when screen initializes
    WidgetsBinding.instance.addPostFrameCallback((_) {
      FocusScope.of(context).unfocus();
    });
  }

  @override
  void dispose() {
    scrollController.dispose();
    searchFocusNode.dispose();
    super.dispose();
  }

  scrollListener() {
    if (scrollController.position.pixels ==
            scrollController.position.maxScrollExtent &&
        controller.totalMembers.value > controller.members.length) {
      controller.fetchMembers(controller.searchFieldController.text,
          page: controller.currentHostPage.value + 1);
      controller.currentHostPage.value++; // Increment the page counter
    }
  }

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: () => context.hideKeyboard(),
      child: Scaffold(
          backgroundColor: ColorConstants.grayFillColor,
          body: Obx(
            () =>
                controller.isMapView.value ? _buildMapMode() : _buildListMode(),
          )),
    );
  }

  Widget _buildMapMode() {
    return Stack(
      children: [
        Positioned.fill(
          child: Obx(() => GoogleMap(
                zoomControlsEnabled: false,
                mapType: MapType.terrain,
                onMapCreated: controller.onMapCreated,
                markers: controller.markers.value,
                initialCameraPosition: CameraPosition(
                  target: LatLng(37.**************, -122.085749655962),
                  zoom: 14.4746,
                ),
              )),
        ),

        Positioned(
          top: 40,
          left: 20,
          right: 20,
          child: headerControls(),
        ),

        // Align(
        //   alignment: Alignment.bottomCenter,
        //   child: Padding(
        //     padding: const EdgeInsets.symmetric(vertical:20,horizontal: 10),
        //     child: MemberCard(bgColor: ColorConstants.whiteColor,height: 95.h, member: null,),
        //   )
        // ),
      ],
    );
  }

  Widget _buildListMode() {
    return Padding(
      padding: PaddingConstants.screenPaddingHalf,
      child: Column(
        children: [
          Padding(
            padding: const EdgeInsets.only(top: 30.0),
            child: headerControls(),
          ),
          buildMusiciansList(),
        ],
      ),
    );
  }

  Widget buildMusiciansList() {
    return Obx(
      () {
        return Expanded(
          child: ListView(padding: EdgeInsets.zero,
            shrinkWrap: true,
            controller: scrollController,
            children: [Widgets.heightSpaceH2,
              controller.isLoading.value
                  ? const ShimmerListSkeleton()
                  : controller.members.isNotEmpty
                      ? ListView.separated(
                          physics: const NeverScrollableScrollPhysics(),
                          padding: EdgeInsets.zero,
                          shrinkWrap: true,
                          itemBuilder: (context, index) {
                            Members member = controller.members[index];


                            return InkWell(onLongPress: (){

                              controller.toggleSelection(member);

                            },
                                onTap: () {
                                  Get.find<HomeController>()
                                      .selectedMemberCard
                                      .value = member;
                                  Get.find<HomeController>()
                                      .memberDetails
                                      .value = null;
                                  Get.find<HomeController>()
                                      .fetchMemberProfileView();

                                  Get.to(() => MemberDetailView());
                                },
                                child: Obx(
                                    ()=> SearchMemberCard(borderColor:controller.selectedMembers.contains(member)?ColorConstants.redColor: ColorConstants.grayBorderColor ,
                                    member: member,bgColor: Colors.white,
                                    onFavTap: () {
                                       controller.toggleFavorite(member.id.toString(), index);
                                    }, isCompare:controller.selectedMembers.contains(member) ,onCompareTap: () {
                                      controller.toggleSelection(member);
                                    },

                                  ),
                                ));
                          },
                          separatorBuilder: (context, index) {
                            return Widgets.heightSpaceH1;
                          },
                          itemCount: controller.members.length ?? 0)
                      : Widgets.noRecordsFound(
                          title: controller.isSearchActive.value == false
                              ? "filter members here"
                              : "No members"),
              if (controller.isMoreLoading.value) Widgets.moreLoading(),Widgets.heightSpaceH2,
            ],
          ),
        );
      },
    );
  }


  Widget headerControls() {
    return Column(
      children: [
        EntrySearchField(
          controller: controller.searchFieldController,
          focusNode: searchFocusNode,
          onChange: (value) {
            controller.onSearchTextChanged(value!);
          },
          onTrailingTap: () {
            searchFocusNode.unfocus();            context.hideKeyboard();
            Get.to(() => FilterView())?.then((value) {
              // Hide keyboard when returning from filter view
            searchFocusNode.unfocus();
            });
          },
          // Don't set onTap to allow default focus behavior only when field is tapped
          onTap: null,
          prefixIcon: Assets.redSearchIcon,
          color: ColorConstants.redColor,
          suffixIcon: Assets.filterIcon,
          hint: "Search Musician",
        ),
        Widgets.heightSpaceH1,
        Obx(
          () => Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              controller.isMapView.value == false
                  ?  controller.selectedMembers.length == 2?CustomButton(
                      padding: 8,
                      onTap: () {

                        if(Get.find<UserController>().userModel?.role=="client") {

                            controller.fetchMComparison();
                            Get.to(() => ComparisonScreen());
                          }else{
    Get.to(() => SubscribeView());


                        }},
                        icon: SizedBox(
                        height: 15,
                        width: 15,
                        child: Image.asset(Assets.compareIcon),
                        ),
                        label: "Compare",
                        width: 100.w,
                        backgroundColor: ColorConstants.whiteColor,
                        textColor: ColorConstants.greyTextColor,
                        fontSize: 7,
                        iconGap: 4,
                        borderColor:
                       ColorConstants.grayBorderColor,)

                  : Container(): Container(),
              ToggleButtons(
                isSelected:
                    controller.isMapView.value ? [false, true] : [true, false],
                onPressed: (index) {
                  controller.toggleView();
                },
                borderRadius: BorderRadius.circular(8),
                borderWidth: 1,
                borderColor: Colors.grey.shade300,
                selectedBorderColor: ColorConstants.grayBorderColor,
                fillColor: ColorConstants.primaryColor,
                selectedColor: Colors.white,
                disabledColor: Colors.black,
                constraints: BoxConstraints(minHeight: 33, minWidth: 33),
                children: List.generate(2, (index) {
                  IconData iconPath =
                      index == 0 ? Icons.list_alt : Icons.map_outlined;

                  return Icon(
                    iconPath,
                  size: 17,
                  );
                }),
              ),
            ],
          ),
        ),
      ],
    );
  }
}
