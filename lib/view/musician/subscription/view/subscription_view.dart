import 'dart:developer';

import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:music_app/core/constants/assets_constants.dart';
import 'package:music_app/core/constants/color_constants.dart';
import 'package:music_app/core/constants/padding_constants.dart';
import 'package:music_app/core/routes/app_routes.dart';
import 'package:music_app/core/widgets/annual_card.dart';
import 'package:music_app/core/widgets/custom_button.dart';
import 'package:music_app/core/widgets/subscription_card.dart';
import 'package:music_app/core/widgets/text_widgets.dart';
import 'package:music_app/core/widgets/widgets.dart';
import 'package:music_app/view/musician/subscription/controller/subscription_view_controller.dart';
import 'package:music_app/view/musician/subscription/model/subcription_model.dart';
import 'package:music_app/view/musician/subscription/view/payment_client_process_view.dart';
import 'package:music_app/view/musician/subscription/view/payment_client_success_view.dart';
import 'package:music_app/view/musician/subscription/view/payment_musician_view.dart';

import '../../../../controller/user_controller.dart';
import '../../../../core/widgets/shimmer_effect.dart';

class SubscriptionView extends StatelessWidget {
  SubscriptionView({super.key});
  final SubscriptionViewController subscriptionViewController =
      Get.put(SubscriptionViewController());
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        centerTitle: true,
        title: Texts.textBold("Subscription", size: 22),
        elevation: 0,
        scrolledUnderElevation: 0,
        backgroundColor: ColorConstants.transparentColor,
      ),
      body: Padding(
        padding: PaddingConstants.screenPaddingHalf
            .copyWith(left: 12, right: 12, top: 0),
        child: Column(
          children: [
            Texts.textNormal('Pick a best membership that fits  you with',
                size: 14, color: ColorConstants.greyTextColor),
            Widgets.heightSpaceH2,
            Obx(() => Expanded(
                  child: subscriptionViewController.isLoading.value
                      ? Padding(
                          padding: const EdgeInsets.all(15.0),
                          child: const ShimmerListSkeleton(),
                        )
                      : subscriptionViewController.subscriptions.isNotEmpty
                          ? ListView.separated(
                              padding: EdgeInsets.zero,
                              shrinkWrap: true,
                              itemBuilder: (context, index) {
                                SubscriptionContent? subscription =
                                    subscriptionViewController
                                        .subscriptions[index];
                                return Obx(() => SubscriptionCard(
                                    isSelected: subscriptionViewController
                                            .selectedSubscriptionIndex.value ==
                                        index,
                                    subscriptionContent: subscription,
                                    onTap: () {
                                      subscriptionViewController
                                          .selectedSubscriptionIndex
                                          .value = index; subscriptionViewController
                                          .selectedSubscriptionId
                                          .value = subscription.id!;
                                    }));
                              },
                              separatorBuilder: (context, index) {
                                return Widgets.heightSpaceH1;
                              },
                              itemCount: subscriptionViewController
                                      .subscriptions.length ??
                                  0)
                          : Widgets.noRecordsFound(title: "No data"),
                )),
            Obx(() => CustomButton(
                  label: "Subscribe Now",
                  textColor: subscriptionViewController
                              .selectedSubscriptionIndex.value ==
                          -1
                      ? Colors.grey
                      : ColorConstants.whiteColor,
                  backgroundColor: subscriptionViewController
                              .selectedSubscriptionIndex.value ==
                          -1
                      ? ColorConstants.disabledColor
                      : ColorConstants.primaryColor,
                  radius: 10,
                  onTap: () {

                    log(Get.find<UserController>().userModel!.name.toString());
                    if (subscriptionViewController
                            .selectedSubscriptionIndex.value ==
                        -1) {
                    } else {
                      Get.to(() =>Get.find<UserController>().userModel?.role=="client"? PaymentClientProcessView(firstTIme: true,
                        url: "https://musicianow.ca/mobile/subscribe/${Get.find<UserController>().token}/${subscriptionViewController.selectedSubscriptionId.value}",
                      ):PaymentMusicianProcessView(firstTime: true,
                            url: "https://musicianow.ca/mobile/subscribe/${Get.find<UserController>().token}/${subscriptionViewController.selectedSubscriptionId.value}",
                          ));
                    }
                  },
                )),
          ],
        ),
      ),
    );
  }
}
