import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:music_app/core/constants/assets_constants.dart';
import 'package:music_app/core/constants/color_constants.dart';
import 'package:music_app/core/constants/padding_constants.dart';
import 'package:music_app/core/routes/app_routes.dart';
import 'package:music_app/core/widgets/custom_button.dart';
import 'package:music_app/core/widgets/text_widgets.dart';
import 'package:music_app/core/widgets/widgets.dart';
import 'package:music_app/view/musician/create_musician_profile/musician_profile_screen/view/musician_profile_screen.dart';

import '../../bottom_navigation/view/bottom_bar_view.dart';


class PaymentMusicianSuccessView extends StatelessWidget {
   PaymentMusicianSuccessView({super.key,required this.firstTIme});
bool? firstTIme;
  @override
  Widget build(BuildContext context) {
    return  WillPopScope(
      onWillPop: () async {if(firstTIme==true){
      Get.offAll(()=>MusicianCreationProfileView());
    }else{
      Get.back();
    }

        return false;
      },
      child: Scaffold(
        appBar: AppBar(
          automaticallyImplyLeading: false,
          centerTitle: true,
          title: Texts.textBold("Payment Status", size: 22),
          elevation: 0,
          scrolledUnderElevation: 0,
          backgroundColor: ColorConstants.transparentColor,

        ),
         body: Padding(
        padding: PaddingConstants.screenPaddingHalf,
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Expanded(
              child: ListView(
                children: [
                  Widgets.heightSpaceH4,
                  SizedBox(
                    height: 190.h,
                    width: 190.w,
                    child: Image.asset(Assets.verifiedImage),
                  ),
                  Widgets.heightSpaceH2,
                  Texts.textBold('Payment Completed', size: 22),
                  Widgets.heightSpaceH1,
                  Texts.textNormal(
                    "Your subscription payment has been completed successfully",
                    size: 14,
                    color: ColorConstants.blackColor
                  ),
                  Widgets.heightSpaceH4,

                ],
              ),
            ),
            Widgets.heightSpaceH2,
            CustomButton(
              label: "Next",
              textColor: ColorConstants.whiteColor,
              backgroundColor: ColorConstants.primaryColor,
              radius: 10,
              onTap: () {
                if(firstTIme==true){
                  Get.offAll(()=>MusicianCreationProfileView());
                }else{
                  Get.back();
                }

              },
            ),
            Widgets.heightSpaceH2,
          ],
        ),
      ),
      ),
    );
  }
}
