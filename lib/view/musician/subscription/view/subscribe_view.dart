import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:get/get_core/src/get_main.dart';
import 'package:get/get_state_manager/src/rx_flutter/rx_obx_widget.dart';
import 'package:music_app/view/musician/subscription/view/payment_client_process_view.dart';
import 'package:music_app/view/musician/subscription/view/payment_musician_view.dart';

import '../../../../controller/user_controller.dart';
import '../../../../core/constants/color_constants.dart';
import '../../../../core/constants/padding_constants.dart';
import '../../../../core/widgets/custom_button.dart';
import '../../../../core/widgets/shimmer_effect.dart';
import '../../../../core/widgets/subscription_card.dart';
import '../../../../core/widgets/text_widgets.dart';
import '../../../../core/widgets/widgets.dart';
import '../controller/subscription_view_controller.dart';
import '../model/subcription_model.dart';

class SubscribeView extends StatelessWidget {
  SubscribeView({super.key});
  final SubscriptionViewController subscriptionViewController =
      Get.put(SubscriptionViewController());
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        centerTitle: true,
        title: Texts.textBold("Subscription", size: 22),
        elevation: 0,
        scrolledUnderElevation: 0,
        backgroundColor: ColorConstants.transparentColor,
      ),
      body: Padding(
        padding: PaddingConstants.screenPaddingHalf
            .copyWith(left: 12, right: 12, top: 0),
        child: Column(
          children: [
            Texts.textNormal('To access features please subscribe',
                size: 14, color: ColorConstants.greyTextColor),
            Widgets.heightSpaceH2,
            Obx(() => Expanded(
                  child: subscriptionViewController.isLoading.value
                      ? Padding(
                          padding: const EdgeInsets.all(15.0),
                          child: const ShimmerListSkeleton(),
                        )
                      : subscriptionViewController.subscriptions.isNotEmpty
                          ? ListView.separated(
                              padding: EdgeInsets.zero,
                              shrinkWrap: true,
                              itemBuilder: (context, index) {
                                SubscriptionContent? subscription =
                                    subscriptionViewController
                                        .subscriptions[index];
                                return Obx(() => SubscriptionCard(
                                    isSelected: subscriptionViewController
                                            .selectedSubscriptionIndex.value ==
                                        index,
                                    subscriptionContent: subscription,
                                    onTap: () {
                                      subscriptionViewController
                                          .selectedSubscriptionIndex
                                          .value = index; subscriptionViewController
                                          .selectedSubscriptionId
                                          .value = subscription.id!;
                                    }));
                              },
                              separatorBuilder: (context, index) {
                                return Widgets.heightSpaceH1;
                              },
                              itemCount: subscriptionViewController
                                      .subscriptions.length ??
                                  0)
                          : Widgets.noRecordsFound(title: "No data"),
                )),
            Obx(() => CustomButton(
                  label: "Subscribe Now",
                  textColor: subscriptionViewController
                              .selectedSubscriptionIndex.value ==
                          -1
                      ? Colors.grey
                      : ColorConstants.whiteColor,
                  backgroundColor: subscriptionViewController
                              .selectedSubscriptionIndex.value ==
                          -1
                      ? ColorConstants.disabledColor
                      : ColorConstants.primaryColor,
                  radius: 10,
                  onTap: () {


                    if (subscriptionViewController
                            .selectedSubscriptionIndex.value ==
                        -1) {
                    } else {
                      Get.to(() =>Get.find<UserController>().userModel?.role=="client"? PaymentClientProcessView(
                        url: "https://musicianow.ca/mobile/subscribe/${Get.find<UserController>().token}/${subscriptionViewController.selectedSubscriptionId.value}",
                      ):PaymentMusicianProcessView(
                            url: "https://musicianow.ca/mobile/subscribe/${Get.find<UserController>().token}/${subscriptionViewController.selectedSubscriptionId.value}",
                          ));
                    }
                  },
                )),
          ],
        ),
      ),
    );
  }
}
